{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Bureau\\YOP\\AlNoorEducationalInstitute\\AlNoorEducationalInstitute.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Bureau\\YOP\\AlNoorEducationalInstitute\\AlNoorEducationalInstitute.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Bureau\\YOP\\AlNoorEducationalInstitute\\AlNoorEducationalInstitute.csproj", "projectName": "AlNoorEducationalInstitute", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Bureau\\YOP\\AlNoorEducationalInstitute\\AlNoorEducationalInstitute.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Bureau\\YOP\\AlNoorEducationalInstitute\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "ClosedXML": {"target": "Package", "version": "[0.102.1, )"}, "LiveChartsCore": {"target": "Package", "version": "[2.0.0-rc2, )"}, "LiveChartsCore.SkiaSharpView.WinForms": {"target": "Package", "version": "[2.0.0-rc2, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Quartz": {"target": "Package", "version": "[3.8.0, )"}, "RestSharp": {"target": "Package", "version": "[110.2.0, )"}, "SkiaSharp.Views.WindowsForms": {"target": "Package", "version": "[2.88.6, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.118, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}