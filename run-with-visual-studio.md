# تشغيل النظام باستخدام Visual Studio

## المشكلة الحالية
يبدو أن هناك مشكلة في PATH أو إعدادات PowerShell مع .NET SDK، رغم أنه مثبت.

## الحل الأسهل: استخدام Visual Studio

### الخطوة 1: تحميل Visual Studio Community (مجاني)
1. اذهب إلى: https://visualstudio.microsoft.com/downloads/
2. حمل "Visual Studio Community 2022" (مجاني)
3. أثناء التثبيت، اختر:
   - ✅ ".NET desktop development"
   - ✅ "ASP.NET and web development" (اختياري)

### الخطوة 2: فتح المشروع
1. افتح Visual Studio
2. انقر "Open a project or solution"
3. اختر ملف: `AlNoorEducationalInstitute.sln`

### الخطوة 3: تشغيل النظام
1. اضغط **F5** أو انقر زر "Start"
2. سيت<PERSON> تجميع وتشغيل النظام تلقائياً
3. ستظهر نافذة تسجيل الدخول

### الخطوة 4: تسجيل الدخول
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

---

## إنشاء ملف تنفيذي (من Visual Studio)

### الطريقة 1: النشر من Visual Studio
1. انقر بالزر الأيمن على المشروع
2. اختر "Publish"
3. اختر "Folder" كهدف
4. اختر مجلد الإخراج (مثل `C:\Published\AlNoorSystem`)
5. في الإعدادات:
   - ✅ Target Runtime: `win-x64`
   - ✅ Self-contained: `Yes`
   - ✅ Single file: `Yes`
6. انقر "Publish"

### الطريقة 2: Build للتوزيع
1. في Visual Studio، اختر "Release" بدلاً من "Debug"
2. انقر "Build" > "Build Solution"
3. ستجد الملفات في: `bin\Release\net6.0-windows\`

---

## إنشاء اختصار سطح المكتب (يدوياً)

بعد النشر من Visual Studio:

1. **اذهب إلى مجلد النشر**
2. **انقر بالزر الأيمن على `AlNoorEducationalInstitute.exe`**
3. **اختر "Create shortcut"**
4. **اسحب الاختصار إلى سطح المكتب**
5. **غيّر اسم الاختصار إلى**: "نظام مؤسسة النور التربوي"

---

## مميزات هذه الطريقة

### ✅ المميزات:
- لا تحتاج حل مشاكل PATH
- Visual Studio يدير .NET تلقائياً
- إمكانية التطوير والتعديل
- أدوات تصحيح الأخطاء
- نشر احترافي

### ✅ النتيجة:
- برنامج يعمل بشكل مستقل
- لا يحتاج .NET منفصل (Self-contained)
- ملف واحد قابل للتشغيل
- جاهز للتوزيع

---

## بعد التشغيل الناجح

### ستجد جميع الميزات:
- ✅ نظام تسجيل الدخول
- ✅ إدارة الطلاب والموظفين
- ✅ النظام المالي
- ✅ إدارة الشعار (أدوات > إعدادات المؤسسة)
- ✅ التقارير والإحصائيات

### لإدارة الشعار:
1. سجل دخول كـ admin
2. اذهب إلى "أدوات" > "إعدادات المؤسسة"
3. تبويب "الشعار" > "رفع شعار جديد"

---

## ملاحظات مهمة

1. **Visual Studio Community مجاني** للاستخدام الشخصي والتعليمي
2. **سيحل جميع مشاكل .NET** تلقائياً
3. **يمكن تطوير النظام** وإضافة ميزات جديدة
4. **النشر سهل ومباشر** من داخل البرنامج

---

**هذه الطريقة الأكثر موثوقية لتشغيل النظام!** 🚀
