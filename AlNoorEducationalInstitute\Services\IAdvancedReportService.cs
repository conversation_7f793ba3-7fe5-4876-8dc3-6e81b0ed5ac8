using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models;
using AlNoorEducationalInstitute.Models.Reports;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة التقارير المتقدمة والتحليلية
    /// Advanced and analytical reports service interface
    /// </summary>
    public interface IAdvancedReportService
    {
        // منشئ التقارير المخصصة
        Task<IEnumerable<CustomReportBuilder>> GetSavedReportsAsync(int userId);
        Task<IEnumerable<CustomReportBuilder>> GetPublicReportsAsync();
        Task<IEnumerable<CustomReportBuilder>> GetReportTemplatesAsync();
        Task<int> SaveCustomReportAsync(CustomReportBuilder report);
        Task<bool> UpdateCustomReportAsync(CustomReportBuilder report);
        Task<bool> DeleteCustomReportAsync(int reportId, int userId);
        Task<CustomReportBuilder?> GetCustomReportAsync(int reportId);
        
        // بناء التقارير
        Task<object> ExecuteCustomReportAsync(CustomReportBuilder report);
        Task<IEnumerable<ReportField>> GetAvailableFieldsAsync(string dataSource);
        Task<IEnumerable<string>> GetAvailableDataSourcesAsync();
        Task<object> PreviewReportDataAsync(CustomReportBuilder report, int maxRows = 100);
        Task<bool> ValidateReportConfigurationAsync(CustomReportBuilder report);
        
        // التقارير التحليلية
        Task<AnalyticalReport> GenerateStudentPerformanceAnalysisAsync(DateTime startDate, DateTime endDate, int? classId = null);
        Task<AnalyticalReport> GenerateFinancialAnalysisAsync(DateTime startDate, DateTime endDate);
        Task<AnalyticalReport> GenerateAttendanceAnalysisAsync(DateTime startDate, DateTime endDate, int? classId = null);
        Task<AnalyticalReport> GenerateEnrollmentTrendsAnalysisAsync(int years = 5);
        Task<AnalyticalReport> GenerateTeacherPerformanceAnalysisAsync(DateTime startDate, DateTime endDate);
        
        // مقارنات الأداء عبر الزمن
        Task<PerformanceComparison> CompareAcademicPerformanceAsync(ComparisonPeriod period, int periods = 12);
        Task<PerformanceComparison> CompareFinancialPerformanceAsync(ComparisonPeriod period, int periods = 12);
        Task<PerformanceComparison> CompareAttendanceRatesAsync(ComparisonPeriod period, int periods = 12);
        Task<PerformanceComparison> CompareEnrollmentNumbersAsync(ComparisonPeriod period, int periods = 12);
        
        // تحليل الطلاب الجدد مقابل القدامى
        Task<StudentCohortAnalysis> AnalyzeStudentCohortsAsync(string academicYear);
        Task<IEnumerable<StudentCohortAnalysis>> CompareStudentCohortsAsync(IEnumerable<string> academicYears);
        Task<object> AnalyzeNewVsReturningStudentsAsync(string academicYear);
        Task<object> AnalyzeStudentRetentionRatesAsync(int years = 5);
        Task<object> AnalyzeStudentProgressionAsync(int studentId, int years = 3);
        
        // تحليل أداء المواد
        Task<object> AnalyzeSubjectPerformanceTrendsAsync(int subjectId, int semesters = 6);
        Task<object> CompareSubjectPerformanceAsync(IEnumerable<int> subjectIds, Semester semester, string academicYear);
        Task<object> AnalyzeSubjectDifficultyAsync(Semester semester, string academicYear);
        Task<object> AnalyzeTeacherEffectivenessAsync(int teacherId, Semester semester, string academicYear);
        
        // تحليل الحضور المتقدم
        Task<object> AnalyzeAttendancePatternsAsync(DateTime startDate, DateTime endDate);
        Task<object> AnalyzeAbsenteeismRisksAsync(DateTime startDate, DateTime endDate);
        Task<object> AnalyzeAttendanceByDayOfWeekAsync(DateTime startDate, DateTime endDate);
        Task<object> AnalyzeAttendanceByWeatherAsync(DateTime startDate, DateTime endDate);
        
        // التحليل المالي المتقدم
        Task<object> AnalyzeRevenueStreamsAsync(DateTime startDate, DateTime endDate);
        Task<object> AnalyzePaymentPatternsAsync(DateTime startDate, DateTime endDate);
        Task<object> AnalyzeOutstandingBalancesAsync();
        Task<object> AnalyzeFeeCollectionEfficiencyAsync(DateTime startDate, DateTime endDate);
        Task<object> PredictCashFlowAsync(int monthsAhead = 6);
        
        // التحليل الإحصائي
        Task<object> PerformCorrelationAnalysisAsync(string metric1, string metric2, DateTime startDate, DateTime endDate);
        Task<object> PerformRegressionAnalysisAsync(string dependentVariable, IEnumerable<string> independentVariables);
        Task<object> PerformClusterAnalysisAsync(string dataSet, IEnumerable<string> features);
        Task<object> DetectAnomaliesAsync(string metric, DateTime startDate, DateTime endDate);
        
        // التنبؤ والتوقعات
        Task<object> PredictStudentEnrollmentAsync(int monthsAhead = 12);
        Task<object> PredictAcademicPerformanceAsync(int studentId, Semester targetSemester);
        Task<object> PredictAttendanceRatesAsync(int weeksAhead = 4);
        Task<object> PredictFinancialPerformanceAsync(int monthsAhead = 6);
        Task<object> PredictStudentDropoutRiskAsync(int studentId);
        
        // تحليل المقارنات المعيارية
        Task<object> BenchmarkAcademicPerformanceAsync(string academicYear);
        Task<object> BenchmarkFinancialPerformanceAsync(int year);
        Task<object> BenchmarkAttendanceRatesAsync(string academicYear);
        Task<object> BenchmarkTeacherPerformanceAsync(string academicYear);
        
        // تصدير التقارير
        Task<byte[]> ExportReportToExcelAsync(object reportData, string reportName);
        Task<byte[]> ExportReportToPdfAsync(object reportData, string reportName);
        Task<byte[]> ExportReportToCsvAsync(object reportData, string reportName);
        Task<string> ExportReportToHtmlAsync(object reportData, string reportName);
        Task<string> ExportReportToJsonAsync(object reportData);
        
        // إدارة قوالب التقارير
        Task<IEnumerable<object>> GetReportTemplatesAsync(ReportType type);
        Task<int> CreateReportTemplateAsync(CustomReportBuilder template);
        Task<bool> UpdateReportTemplateAsync(CustomReportBuilder template);
        Task<bool> DeleteReportTemplateAsync(int templateId);
        Task<CustomReportBuilder?> CloneReportTemplateAsync(int templateId, string newName);
        
        // جدولة التقارير
        Task<int> ScheduleReportAsync(int reportId, string cronExpression, string recipients);
        Task<bool> UpdateScheduledReportAsync(int scheduleId, string cronExpression, string recipients);
        Task<bool> DeleteScheduledReportAsync(int scheduleId);
        Task<IEnumerable<object>> GetScheduledReportsAsync(int userId);
        Task ExecuteScheduledReportsAsync();
        
        // إدارة الأداء
        Task<object> GetReportExecutionStatisticsAsync();
        Task<bool> OptimizeReportPerformanceAsync(int reportId);
        Task ClearReportCacheAsync();
        Task<TimeSpan> GetAverageReportExecutionTimeAsync(ReportType type);
        
        // التحقق من صحة البيانات
        Task<object> ValidateDataQualityAsync(string dataSource);
        Task<object> DetectDataInconsistenciesAsync();
        Task<bool> RepairDataInconsistenciesAsync();
        Task<object> GetDataQualityReportAsync();
        
        // التحليل التفاعلي
        Task<object> CreateInteractiveReportAsync(CustomReportBuilder report);
        Task<object> UpdateInteractiveReportFiltersAsync(int reportId, Dictionary<string, object> filters);
        Task<object> DrillDownReportDataAsync(int reportId, string dimension, object value);
        Task<object> DrillUpReportDataAsync(int reportId, string dimension);
        
        // تحليل الاتجاهات المتقدم
        Task<object> AnalyzeLongTermTrendsAsync(string metric, int years = 5);
        Task<object> AnalyzeSeasonalPatternsAsync(string metric, int years = 3);
        Task<object> AnalyzeCyclicalPatternsAsync(string metric, int years = 5);
        Task<object> ForecastTrendsAsync(string metric, int periodsAhead = 12);
        
        // تحليل المخاطر
        Task<object> AssessAcademicRisksAsync();
        Task<object> AssessFinancialRisksAsync();
        Task<object> AssessOperationalRisksAsync();
        Task<object> GenerateRiskMitigationReportAsync();
        
        // تحليل الفرص
        Task<object> IdentifyGrowthOpportunitiesAsync();
        Task<object> IdentifyEfficiencyOpportunitiesAsync();
        Task<object> IdentifyRevenueOpportunitiesAsync();
        Task<object> GenerateOpportunityActionPlanAsync();
    }
}
