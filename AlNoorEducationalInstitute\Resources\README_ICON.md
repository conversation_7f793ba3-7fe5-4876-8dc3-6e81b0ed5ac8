# إرشادات إنشاء أيقونة التطبيق

## المطلوب
ملف أيقونة بصيغة .ico باسم `app-icon.ico` في هذا المجلد.

## المواصفات الموصى بها
- **الحجم**: 256x256 بكسل (مع أحجام متعددة: 16x16, 32x32, 48x48, 256x256)
- **الصيغة**: .ico
- **التصميم**: شعار مؤسسة النور التربوي أو رمز تعليمي

## طرق إنشاء الأيقونة

### الطريقة الأولى: استخدام أدوات مجانية
1. **GIMP** (مجاني):
   - افتح GIMP
   - أنشئ صورة جديدة 256x256
   - صمم الأيقونة
   - احفظ باسم app-icon.ico

2. **Paint.NET** مع إضافة ICO:
   - حمل Paint.NET
   - حمل إضافة ICO plugin
   - صمم الأيقونة واحفظها

### الطريقة الثانية: مواقع مجانية
1. **Favicon.io**:
   - اذهب إلى favicon.io
   - استخدم Text to Icon أو Image to Icon
   - حمل ملف .ico

2. **ConvertICO.com**:
   - ارفع صورة PNG أو JPG
   - حولها إلى .ico

### الطريقة الثالثة: استخدام شعار موجود
إذا كان لديك شعار المؤسسة:
1. احفظه بصيغة PNG بحجم 256x256
2. استخدم أي من المواقع أعلاه لتحويله إلى .ico

## ملاحظة مؤقتة
حالياً، سيعمل التطبيق بدون أيقونة مخصصة (سيستخدم الأيقونة الافتراضية لـ .NET).
لإضافة الأيقونة المخصصة، ضع ملف `app-icon.ico` في هذا المجلد.

## تحديث المشروع بعد إضافة الأيقونة
بعد إضافة ملف app-icon.ico، قم بإعادة بناء المشروع:
```
dotnet build --configuration Release
```
