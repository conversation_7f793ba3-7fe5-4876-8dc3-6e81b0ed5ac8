@echo off
echo ========================================
echo    Publishing Al-Noor Educational System
echo ========================================
echo.

echo Cleaning project...
cd AlNoorEducationalInstitute
dotnet clean --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Clean failed
    pause
    exit /b 1
)

echo.
echo Publishing application...
echo.

dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Publish failed
    pause
    exit /b 1
)

echo.
echo Application published successfully
echo.
echo Published folder: Published\AlNoorSystem
echo.
echo Executable file: AlNoorEducationalInstitute.exe
echo.

cd ..
echo Creating desktop shortcut...

powershell -Command "& {
    $WshShell = New-Object -comObject WScript.Shell;
    $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Al-Noor Educational System.lnk');
    $Shortcut.TargetPath = '%CD%\Published\AlNoorSystem\AlNoorEducationalInstitute.exe';
    $Shortcut.WorkingDirectory = '%CD%\Published\AlNoorSystem';
    $Shortcut.Description = 'Al-Noor Educational Institute Management System';
    $Shortcut.Save()
}"

if %ERRORLEVEL% EQU 0 (
    echo Desktop shortcut created
) else (
    echo Desktop shortcut not created
)

echo.
echo Publishing complete! You can now run the application from:
echo    - Desktop: Al-Noor Educational System
echo    - Or directly from: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
echo.
pause
