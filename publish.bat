@echo off
chcp 65001 > nul
echo ========================================
echo    نشر نظام مؤسسة النور التربوي
echo    Publishing Al-Noor Educational System
echo ========================================
echo.

echo جاري تنظيف المشروع...
echo Cleaning project...
cd AlNoorEducationalInstitute
dotnet clean --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في تنظيف المشروع
    echo ❌ Clean failed
    pause
    exit /b 1
)

echo.
echo جاري نشر التطبيق...
echo Publishing application...
echo.

dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في نشر التطبيق
    echo ❌ Publish failed
    pause
    exit /b 1
)

echo.
echo ✅ تم نشر التطبيق بنجاح
echo ✅ Application published successfully
echo.
echo 📁 مجلد النشر: Published\AlNoorSystem
echo 📁 Published folder: Published\AlNoorSystem
echo.
echo 🚀 ملف التشغيل: AlNoorEducationalInstitute.exe
echo 🚀 Executable file: AlNoorEducationalInstitute.exe
echo.

cd ..
echo جاري إنشاء اختصار سطح المكتب...
echo Creating desktop shortcut...

powershell -Command "& {
    $WshShell = New-Object -comObject WScript.Shell;
    $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام مؤسسة النور التربوي.lnk');
    $Shortcut.TargetPath = '%CD%\Published\AlNoorSystem\AlNoorEducationalInstitute.exe';
    $Shortcut.WorkingDirectory = '%CD%\Published\AlNoorSystem';
    $Shortcut.Description = 'نظام إدارة مؤسسة النور التربوي';
    $Shortcut.Save()
}"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء اختصار سطح المكتب
    echo ✅ Desktop shortcut created
) else (
    echo ⚠️ لم يتم إنشاء اختصار سطح المكتب
    echo ⚠️ Desktop shortcut not created
)

echo.
echo 🎉 اكتمل النشر! يمكنك الآن تشغيل التطبيق من:
echo 🎉 Publishing complete! You can now run the application from:
echo    - سطح المكتب: نظام مؤسسة النور التربوي
echo    - Desktop: نظام مؤسسة النور التربوي
echo    - أو مباشرة من: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
echo    - Or directly from: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
echo.
pause
