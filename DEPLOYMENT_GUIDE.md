# دليل النشر والتثبيت - نظام مؤسسة النور التربوي

## نظرة عامة
هذا الدليل يوضح كيفية تحويل نظام مؤسسة النور التربوي إلى برنامج مكتبي جاهز للتشغيل مع ملف تثبيت احترافي.

## متطلبات النشر

### للمطور
- .NET 6.0 SDK أو أحدث
- Windows 10/11
- PowerShell 5.0 أو أحدث
- صلاحيات المدير (للاختبار)

### للمستخدم النهائي
- Windows 10 أو أحدث
- 500 ميجابايت مساحة فارغة
- صلاحيات المدير (للتثبيت فقط)

## خطوات النشر (للمطور)

### الخطوة 1: تجهيز المشروع
```bash
# التأكد من عدم وجود أخطاء
dotnet build --configuration Release

# تنظيف المشروع
dotnet clean --configuration Release
```

### الخطوة 2: نشر التطبيق
```bash
# تشغيل ملف النشر التلقائي
publish.bat
```

أو يدوياً:
```bash
cd AlNoorEducationalInstitute
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem
```

### الخطوة 3: إنشاء ملف التثبيت
```bash
# تشغيل منشئ المثبت
install.bat
```

## ملفات النشر المُنشأة

### ملفات النشر الأساسية
- `publish.bat` - ملف نشر التطبيق
- `install.bat` - ملف التثبيت الرئيسي
- `create-installer.ps1` - نص PowerShell للتثبيت المتقدم

### مجلدات النشر
- `Published\AlNoorSystem\` - ملفات التطبيق المنشورة
- `Properties\PublishProfiles\` - ملفات إعداد النشر

### ملفات التكوين
- `AlNoorEducationalInstitute.csproj` - محدث بإعدادات النشر
- `WindowsDesktop.pubxml` - ملف تعريف النشر

## عملية التثبيت (للمستخدم النهائي)

### الطريقة الأولى: التثبيت الكامل
1. تشغيل `install.bat` كمدير
2. اتباع التعليمات على الشاشة
3. البرنامج سيُثبت في `Program Files\Al-Noor Educational Institute`
4. سيتم إنشاء اختصارات في:
   - سطح المكتب
   - قائمة ابدأ
   - قائمة البرامج المثبتة

### الطريقة الثانية: التشغيل المباشر
1. تشغيل `publish.bat`
2. الانتقال إلى `Published\AlNoorSystem\`
3. تشغيل `AlNoorEducationalInstitute.exe` مباشرة

## ميزات النشر

### النشر الذاتي (Self-Contained)
- ✅ لا يتطلب تثبيت .NET منفصل
- ✅ يعمل على أي جهاز Windows 10+
- ✅ جميع المكتبات مضمنة

### ملف واحد (Single File)
- ✅ ملف تنفيذي واحد
- ✅ سهولة التوزيع
- ✅ تشغيل سريع

### التحسينات
- ✅ ReadyToRun للأداء الأفضل
- ✅ ضغط المكتبات الأصلية
- ✅ تحسين الذاكرة

## إعدادات التطبيق

### معلومات التطبيق
- **الاسم**: نظام إدارة مؤسسة النور التربوي
- **الإصدار**: 1.0.0.0
- **الناشر**: مؤسسة النور التربوي
- **الوصف**: نظام إدارة شامل لمؤسسة النور التربوي

### الملفات المضمنة
- التطبيق الرئيسي
- قاعدة البيانات (SQLite)
- ملفات الإعدادات
- المكتبات المطلوبة

## استكشاف الأخطاء

### مشاكل النشر الشائعة

**1. خطأ: .NET SDK غير موجود**
```
الحل: تثبيت .NET 6.0 SDK من microsoft.com/dotnet
```

**2. خطأ: صلاحيات غير كافية**
```
الحل: تشغيل Command Prompt كمدير
```

**3. خطأ: مساحة غير كافية**
```
الحل: توفير 2 جيجابايت على الأقل للنشر
```

### مشاكل التثبيت الشائعة

**1. لا يعمل المثبت**
```
الحل: تشغيل install.bat كمدير
```

**2. لا تظهر الاختصارات**
```
الحل: إعادة تشغيل المثبت أو إنشاء اختصار يدوياً
```

**3. خطأ في تشغيل التطبيق**
```
الحل: التأكد من Windows 10+ وإعادة التثبيت
```

## التوزيع

### للمؤسسات الصغيرة
- نسخ مجلد `Published\AlNoorSystem` على مفتاح USB
- تشغيل `AlNoorEducationalInstitute.exe` مباشرة

### للمؤسسات الكبيرة
- استخدام `install.bat` للتثبيت المركزي
- نشر عبر الشبكة الداخلية
- استخدام Group Policy للتثبيت التلقائي

### للتحديثات
- إعادة نشر النسخة الجديدة
- استبدال الملفات في مجلد التثبيت
- أو إعادة تشغيل المثبت

## الأمان

### حماية البيانات
- قاعدة البيانات محمية محلياً
- لا توجد اتصالات خارجية
- البيانات مشفرة في قاعدة البيانات

### صلاحيات النظام
- يتطلب صلاحيات المدير للتثبيت فقط
- يعمل بصلاحيات المستخدم العادي بعد التثبيت
- لا يحتاج اتصال بالإنترنت

## الدعم الفني

### ملفات السجل
- `Logs\` - سجلات التطبيق
- `Database\` - ملفات قاعدة البيانات
- `Backup\` - النسخ الاحتياطية

### معلومات النظام
- إصدار Windows
- مساحة القرص المتاحة
- ذاكرة النظام

---

## ملاحظات مهمة

1. **النسخ الاحتياطي**: احرص على أخذ نسخة احتياطية من قاعدة البيانات قبل التحديث
2. **الاختبار**: اختبر التطبيق على أجهزة مختلفة قبل التوزيع
3. **التوثيق**: احتفظ بنسخة من هذا الدليل مع كل إصدار
4. **الدعم**: وفر معلومات الاتصال للدعم الفني

**تاريخ آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0
