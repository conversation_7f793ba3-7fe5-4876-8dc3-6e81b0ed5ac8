# دليل تثبيت .NET SDK - نظام مؤسسة النور التربوي

## المشكلة الحالية
```
'dotnet' n'est pas reconnu en tant que commande interne
```

هذا يعني أن .NET SDK غير مثبت على النظام.

## الحل: تثبيت .NET SDK

### الخطوة 1: تحميل .NET SDK

1. **اذهب إلى الموقع الرسمي:**
   - https://dotnet.microsoft.com/download

2. **اختر .NET 6.0 أو أحدث:**
   - انقر على "Download .NET 6.0"
   - اختر "SDK" (وليس Runtime فقط)
   - اختر "Windows x64" للأنظمة 64-bit

3. **حمل الملف:**
   - سيكون اسمه مثل: `dotnet-sdk-6.0.xxx-win-x64.exe`

### الخطوة 2: تثبيت .NET SDK

1. **شغل ملف التثبيت:**
   - انقر مرتين على الملف المحمل
   - اتبع التعليمات على الشاشة
   - انقر "Install" و انتظر حتى اكتمال التثبيت

2. **أعد تشغيل Command Prompt:**
   - أغلق أي نوافذ Command Prompt مفتوحة
   - افتح Command Prompt جديد

### الخطوة 3: التحقق من التثبيت

افتح Command Prompt واكتب:
```cmd
dotnet --version
```

يجب أن تظهر رسالة مثل:
```
6.0.xxx
```

### الخطوة 4: تشغيل النظام

بعد تثبيت .NET SDK بنجاح، يمكنك تشغيل:

```cmd
# للتشغيل السريع
run-app.bat

# أو للتثبيت الكامل
simple-install.bat
```

---

## حلول بديلة (إذا لم تستطع تثبيت .NET SDK)

### الحل البديل 1: استخدام Visual Studio

1. **حمل Visual Studio Community (مجاني):**
   - https://visualstudio.microsoft.com/downloads/
   - اختر "Community" (مجاني للاستخدام الشخصي والتعليمي)

2. **أثناء التثبيت:**
   - اختر ".NET desktop development"
   - سيتم تثبيت .NET SDK تلقائياً

3. **تشغيل المشروع:**
   - افتح ملف `AlNoorEducationalInstitute.sln`
   - اضغط F5 أو انقر "Start"

### الحل البديل 2: نسخة محمولة جاهزة

إذا كان لديك جهاز آخر عليه .NET SDK:

1. **على الجهاز الذي عليه .NET:**
   ```cmd
   cd AlNoorEducationalInstitute
   dotnet publish -c Release -r win-x64 --self-contained true -o ..\Portable
   ```

2. **انسخ مجلد `Portable` إلى الجهاز المستهدف**

3. **شغل `AlNoorEducationalInstitute.exe` مباشرة**

---

## معلومات إضافية

### متطلبات النظام لـ .NET 6.0:
- **نظام التشغيل**: Windows 10 version 1607 أو أحدث
- **المعمارية**: x64, x86, ARM64
- **المساحة**: حوالي 650 MB

### أنواع .NET المختلفة:
- **.NET SDK**: للتطوير والبناء (هذا ما نحتاجه)
- **.NET Runtime**: للتشغيل فقط
- **.NET Desktop Runtime**: للتطبيقات المكتبية

### التحقق من إصدار Windows:
```cmd
winver
```

---

## استكشاف الأخطاء

### إذا لم يعمل `dotnet --version` بعد التثبيت:

1. **أعد تشغيل الكمبيوتر**
2. **تحقق من PATH:**
   ```cmd
   echo %PATH%
   ```
   يجب أن تجد مسار مثل: `C:\Program Files\dotnet\`

3. **تثبيت يدوي لـ PATH:**
   - افتح "System Properties" > "Environment Variables"
   - أضف `C:\Program Files\dotnet\` إلى PATH

### إذا ظهرت رسائل خطأ أثناء التثبيت:

1. **تأكد من صلاحيات المدير**
2. **أغلق برامج مكافحة الفيروسات مؤقتاً**
3. **حمل إصدار أقدم من .NET (مثل .NET 6.0.0)**

---

## بعد تثبيت .NET بنجاح

ستتمكن من:
- ✅ تشغيل `run-app.bat` للتشغيل السريع
- ✅ تشغيل `simple-install.bat` للتثبيت الكامل
- ✅ تطوير وتعديل النظام
- ✅ إنشاء نسخ محمولة للتوزيع

**ملاحظة**: تثبيت .NET SDK مرة واحدة فقط كافي لجميع مشاريع .NET على الجهاز.

---

**تاريخ آخر تحديث**: ديسمبر 2024
