# نص PowerShell لإنشاء مثبت نظام مؤسسة النور التربوي
# PowerShell script to create Al-Noor Educational Institute installer

param(
    [string]$InstallPath = "$env:ProgramFiles\Al-Noor Educational Institute",
    [string]$ShortcutName = "نظام مؤسسة النور التربوي"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "    مثبت نظام مؤسسة النور التربوي" -ForegroundColor Green
Write-Host "  Al-Noor Educational Institute Installer" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "⚠️  هذا المثبت يتطلب صلاحيات المدير" -ForegroundColor Yellow
    Write-Host "⚠️  This installer requires administrator privileges" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "جاري إعادة التشغيل بصلاحيات المدير..." -ForegroundColor Yellow
    Write-Host "Restarting with administrator privileges..." -ForegroundColor Yellow
    
    Start-Process PowerShell -Verb RunAs "-File `"$PSCommandPath`" `"$InstallPath`" `"$ShortcutName`""
    exit
}

Write-Host "✅ تم التحقق من صلاحيات المدير" -ForegroundColor Green
Write-Host "✅ Administrator privileges verified" -ForegroundColor Green
Write-Host ""

# إنشاء مجلد التثبيت
Write-Host "📁 إنشاء مجلد التثبيت..." -ForegroundColor Cyan
Write-Host "📁 Creating installation directory..." -ForegroundColor Cyan

try {
    if (Test-Path $InstallPath) {
        Write-Host "⚠️  المجلد موجود مسبقاً، سيتم استبداله" -ForegroundColor Yellow
        Write-Host "⚠️  Directory exists, will be replaced" -ForegroundColor Yellow
        Remove-Item $InstallPath -Recurse -Force
    }
    
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    Write-Host "✅ تم إنشاء مجلد التثبيت: $InstallPath" -ForegroundColor Green
    Write-Host "✅ Installation directory created: $InstallPath" -ForegroundColor Green
}
catch {
    Write-Host "❌ فشل في إنشاء مجلد التثبيت: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Failed to create installation directory: $($_.Exception.Message)" -ForegroundColor Red
    pause
    exit 1
}

# نسخ ملفات التطبيق
Write-Host ""
Write-Host "📋 نسخ ملفات التطبيق..." -ForegroundColor Cyan
Write-Host "📋 Copying application files..." -ForegroundColor Cyan

$SourcePath = ".\Published\AlNoorSystem\*"
if (-not (Test-Path ".\Published\AlNoorSystem")) {
    Write-Host "❌ مجلد النشر غير موجود. يرجى تشغيل publish.bat أولاً" -ForegroundColor Red
    Write-Host "❌ Published folder not found. Please run publish.bat first" -ForegroundColor Red
    pause
    exit 1
}

try {
    Copy-Item $SourcePath $InstallPath -Recurse -Force
    Write-Host "✅ تم نسخ ملفات التطبيق" -ForegroundColor Green
    Write-Host "✅ Application files copied" -ForegroundColor Green
}
catch {
    Write-Host "❌ فشل في نسخ الملفات: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Failed to copy files: $($_.Exception.Message)" -ForegroundColor Red
    pause
    exit 1
}

# إنشاء اختصار سطح المكتب
Write-Host ""
Write-Host "🔗 إنشاء اختصار سطح المكتب..." -ForegroundColor Cyan
Write-Host "🔗 Creating desktop shortcut..." -ForegroundColor Cyan

try {
    $WshShell = New-Object -comObject WScript.Shell
    $DesktopPath = [System.Environment]::GetFolderPath('Desktop')
    $ShortcutPath = "$DesktopPath\$ShortcutName.lnk"
    $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
    $Shortcut.TargetPath = "$InstallPath\AlNoorEducationalInstitute.exe"
    $Shortcut.WorkingDirectory = $InstallPath
    $Shortcut.Description = "نظام إدارة مؤسسة النور التربوي"
    $Shortcut.Save()
    
    Write-Host "✅ تم إنشاء اختصار سطح المكتب" -ForegroundColor Green
    Write-Host "✅ Desktop shortcut created" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  لم يتم إنشاء اختصار سطح المكتب: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "⚠️  Desktop shortcut not created: $($_.Exception.Message)" -ForegroundColor Yellow
}

# إنشاء اختصار في قائمة ابدأ
Write-Host ""
Write-Host "📋 إنشاء اختصار في قائمة ابدأ..." -ForegroundColor Cyan
Write-Host "📋 Creating Start Menu shortcut..." -ForegroundColor Cyan

try {
    $StartMenuPath = "$env:ProgramData\Microsoft\Windows\Start Menu\Programs"
    $StartMenuShortcut = "$StartMenuPath\$ShortcutName.lnk"
    $Shortcut = $WshShell.CreateShortcut($StartMenuShortcut)
    $Shortcut.TargetPath = "$InstallPath\AlNoorEducationalInstitute.exe"
    $Shortcut.WorkingDirectory = $InstallPath
    $Shortcut.Description = "نظام إدارة مؤسسة النور التربوي"
    $Shortcut.Save()
    
    Write-Host "✅ تم إنشاء اختصار في قائمة ابدأ" -ForegroundColor Green
    Write-Host "✅ Start Menu shortcut created" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  لم يتم إنشاء اختصار في قائمة ابدأ: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "⚠️  Start Menu shortcut not created: $($_.Exception.Message)" -ForegroundColor Yellow
}

# إضافة إلى قائمة البرامج المثبتة (اختياري)
Write-Host ""
Write-Host "📝 تسجيل البرنامج في النظام..." -ForegroundColor Cyan
Write-Host "📝 Registering application in system..." -ForegroundColor Cyan

try {
    $RegPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\AlNoorEducationalInstitute"
    New-Item -Path $RegPath -Force | Out-Null
    Set-ItemProperty -Path $RegPath -Name "DisplayName" -Value "نظام مؤسسة النور التربوي"
    Set-ItemProperty -Path $RegPath -Name "DisplayVersion" -Value "1.0.0"
    Set-ItemProperty -Path $RegPath -Name "Publisher" -Value "مؤسسة النور التربوي"
    Set-ItemProperty -Path $RegPath -Name "InstallLocation" -Value $InstallPath
    Set-ItemProperty -Path $RegPath -Name "UninstallString" -Value "powershell.exe -File `"$InstallPath\uninstall.ps1`""
    
    Write-Host "✅ تم تسجيل البرنامج في النظام" -ForegroundColor Green
    Write-Host "✅ Application registered in system" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  لم يتم تسجيل البرنامج: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "⚠️  Application not registered: $($_.Exception.Message)" -ForegroundColor Yellow
}

# إنشاء ملف إلغاء التثبيت
$UninstallScript = @"
# ملف إلغاء تثبيت نظام مؤسسة النور التربوي
Write-Host "إلغاء تثبيت نظام مؤسسة النور التربوي..." -ForegroundColor Yellow
Remove-Item "$InstallPath" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item "$env:USERPROFILE\Desktop\$ShortcutName.lnk" -ErrorAction SilentlyContinue
Remove-Item "$env:ProgramData\Microsoft\Windows\Start Menu\Programs\$ShortcutName.lnk" -ErrorAction SilentlyContinue
Remove-Item "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\AlNoorEducationalInstitute" -ErrorAction SilentlyContinue
Write-Host "تم إلغاء التثبيت بنجاح" -ForegroundColor Green
pause
"@

$UninstallScript | Out-File "$InstallPath\uninstall.ps1" -Encoding UTF8

Write-Host ""
Write-Host "🎉 اكتمل التثبيت بنجاح!" -ForegroundColor Green
Write-Host "🎉 Installation completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 مسار التثبيت: $InstallPath" -ForegroundColor Cyan
Write-Host "📍 Installation path: $InstallPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 يمكنك الآن تشغيل البرنامج من:" -ForegroundColor Cyan
Write-Host "🚀 You can now run the program from:" -ForegroundColor Cyan
Write-Host "   • سطح المكتب: $ShortcutName" -ForegroundColor White
Write-Host "   • Desktop: $ShortcutName" -ForegroundColor White
Write-Host "   • قائمة ابدأ: $ShortcutName" -ForegroundColor White
Write-Host "   • Start Menu: $ShortcutName" -ForegroundColor White
Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
Write-Host "Press any key to exit..." -ForegroundColor Gray
pause
