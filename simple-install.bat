@echo off
echo ========================================
echo   Al-Noor Educational Institute
echo   Simple Installation Script
echo ========================================
echo.

echo This script will:
echo 1. Publish the application
echo 2. Create a desktop shortcut
echo 3. Make it ready to run
echo.

set /p confirm="Continue? (Y/N): "
if /i "%confirm%" NEQ "Y" (
    echo Installation cancelled
    pause
    exit /b 0
)

echo.
echo Step 1: Publishing application...
echo.

cd AlNoorEducationalInstitute
dotnet clean --configuration Release > nul 2>&1
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to publish application
    echo Make sure .NET SDK is installed
    pause
    exit /b 1
)

cd ..
echo.
echo Step 2: Creating desktop shortcut...

powershell -Command "try { $WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut([Environment]::GetFolderPath('Desktop') + '\Al-Noor Educational System.lnk'); $Shortcut.TargetPath = (Get-Location).Path + '\Published\AlNoorSystem\AlNoorEducationalInstitute.exe'; $Shortcut.WorkingDirectory = (Get-Location).Path + '\Published\AlNoorSystem'; $Shortcut.Description = 'Al-Noor Educational Institute Management System'; $Shortcut.Save(); Write-Host 'Desktop shortcut created successfully' } catch { Write-Host 'Could not create desktop shortcut' }"

echo.
echo ========================================
echo   Installation Complete!
echo ========================================
echo.
echo The application has been published to:
echo   Published\AlNoorSystem\
echo.
echo You can run it by:
echo   1. Double-clicking the desktop shortcut
echo   2. Or running: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
echo.
echo Default login:
echo   Username: admin
echo   Password: admin123
echo.
pause
