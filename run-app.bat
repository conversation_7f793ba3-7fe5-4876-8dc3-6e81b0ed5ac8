@echo off
echo ========================================
echo   Al-Noor Educational Institute
echo   Quick Start
echo ========================================
echo.

REM Check if already published
if exist "Published\AlNoorSystem\AlNoorEducationalInstitute.exe" (
    echo Application found. Starting...
    echo.
    start "" "Published\AlNoorSystem\AlNoorEducationalInstitute.exe"
    echo Application started!
    echo.
    echo Default login:
    echo   Username: admin
    echo   Password: admin123
    echo.
    pause
    exit /b 0
)

echo Application not found. Publishing first...
echo.

cd AlNoorEducationalInstitute
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to publish application
    echo Please make sure .NET SDK is installed
    pause
    exit /b 1
)

cd ..
echo.
echo Publishing complete! Starting application...
echo.

start "" "Published\AlNoorSystem\AlNoorEducationalInstitute.exe"

echo Application started!
echo.
echo Default login:
echo   Username: admin
echo   Password: admin123
echo.
pause
