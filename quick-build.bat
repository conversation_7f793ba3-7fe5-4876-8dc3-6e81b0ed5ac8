@echo off
echo ========================================
echo   Al-Noor Educational Institute
echo   Quick Build Test
echo ========================================
echo.

echo Testing build process...
echo.

cd AlNoorEducationalInstitute

echo Step 1: Clean project...
dotnet clean --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)

echo.
echo Step 2: Build project...
dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    echo.
    echo Common issues:
    echo 1. Missing using statements
    echo 2. Duplicate class definitions
    echo 3. Missing references
    echo.
    echo Check the error messages above for details.
    pause
    exit /b 1
)

echo.
echo SUCCESS: Build completed successfully!
echo.
echo The project compiles without errors.
echo You can now run the full publish process.
echo.
pause
