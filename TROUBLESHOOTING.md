# دليل استكشاف الأخطاء - نظام مؤسسة النور التربوي

## المشاكل الشائعة وحلولها

### 1. مشكلة ترميز النص العربي في ملفات Batch

**الأعراض:**
```
'لى' n'est pas reconnu en tant que commande interne
'r' n'est pas reconnu en tant que commande interne
```

**السبب:** مشكلة في ترميز النصوص العربية في Windows Command Prompt

**الحل:**
- استخدم `simple-install.bat` بدلاً من `install.bat`
- أو استخدم `run-app.bat` للتشغيل المباشر

### 2. عدم وجود .NET SDK

**الأعراض:**
```
'dotnet' n'est pas reconnu en tant que commande interne
```

**السبب:** .NET SDK غير مثبت أو غير موجود في PATH

**الحلول:**

#### الحل الأول: تثبيت .NET SDK
1. اذهب إلى: https://dotnet.microsoft.com/download
2. حمل .NET 6.0 SDK أو أحدث
3. ثبته واعد تشغيل Command Prompt
4. اختبر بكتابة: `dotnet --version`

#### الحل الثاني: استخدام Visual Studio
1. افتح المشروع في Visual Studio
2. انقر بالزر الأيمن على المشروع
3. اختر "Publish"
4. اختر "Folder" كهدف
5. اختر مجلد الإخراج
6. انقر "Publish"

#### الحل الثالث: النشر اليدوي
إذا كان لديك .NET Runtime فقط:
1. انسخ مجلد المشروع كاملاً
2. ضعه على الجهاز المستهدف
3. تأكد من وجود .NET 6.0 Runtime
4. شغل الملف التنفيذي مباشرة

### 3. مشاكل الصلاحيات

**الأعراض:**
- فشل في إنشاء الاختصارات
- فشل في النسخ إلى Program Files

**الحل:**
1. انقر بالزر الأيمن على Command Prompt
2. اختر "Run as administrator"
3. شغل ملف التثبيت من هناك

### 4. مشاكل PowerShell

**الأعراض:**
```
PowerShell execution policy error
```

**الحل:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## طرق التشغيل البديلة

### الطريقة 1: التشغيل المباشر (الأسهل)
```bash
# إذا كان .NET SDK متوفر
run-app.bat
```

### الطريقة 2: النشر اليدوي
```bash
# في مجلد المشروع
cd AlNoorEducationalInstitute
dotnet publish -c Release -r win-x64 --self-contained true -o ..\Published\AlNoorSystem
```

### الطريقة 3: استخدام Visual Studio
1. افتح `AlNoorEducationalInstitute.sln`
2. Build > Publish AlNoorEducationalInstitute
3. اختر الإعدادات المطلوبة
4. انقر Publish

### الطريقة 4: النسخ المحمول
إذا كان التطبيق منشور مسبقاً:
1. انسخ مجلد `Published\AlNoorSystem`
2. ضعه في أي مكان
3. شغل `AlNoorEducationalInstitute.exe`

## فحص المتطلبات

### فحص .NET
```cmd
dotnet --version
```
يجب أن يظهر: 6.0.x أو أحدث

### فحص PowerShell
```cmd
powershell -Command "Get-Host"
```

### فحص الصلاحيات
```cmd
whoami /groups | find "S-1-5-32-544"
```
إذا ظهرت نتيجة، فلديك صلاحيات مدير

## ملفات التشغيل المتاحة

### للتطوير والاختبار:
- `run-app.bat` - تشغيل سريع
- `simple-install.bat` - تثبيت مبسط
- `publish.bat` - نشر فقط

### للإنتاج:
- `install.bat` - مثبت كامل (يتطلب إصلاح الترميز)
- `create-installer.ps1` - مثبت PowerShell متقدم

## إنشاء نسخة محمولة

إذا كنت تريد نسخة لا تحتاج تثبيت:

1. **نشر التطبيق:**
```cmd
cd AlNoorEducationalInstitute
dotnet publish -c Release -r win-x64 --self-contained true -o ..\Portable\AlNoorSystem
```

2. **إنشاء ملف تشغيل:**
```cmd
echo @echo off > ..\Portable\Start.bat
echo start "" "AlNoorSystem\AlNoorEducationalInstitute.exe" >> ..\Portable\Start.bat
```

3. **ضغط المجلد:**
- اضغط مجلد `Portable` إلى ملف ZIP
- وزعه على الأجهزة المطلوبة

## الدعم الفني

### معلومات مفيدة للدعم:
- إصدار Windows: `winver`
- إصدار .NET: `dotnet --version`
- معمارية النظام: `echo %PROCESSOR_ARCHITECTURE%`
- مساحة القرص: `dir C:\ /-c`

### ملفات السجل:
- `Logs\` - سجلات التطبيق
- Windows Event Viewer - سجلات النظام

### اختبار سريع:
```cmd
# اختبار التطبيق
cd Published\AlNoorSystem
AlNoorEducationalInstitute.exe --version
```

---

## ملاحظات مهمة

1. **النسخ الاحتياطي**: احتفظ بنسخة من قاعدة البيانات قبل أي تحديث
2. **الأمان**: لا تشارك ملفات التطبيق عبر الإنترنت العام
3. **التحديثات**: احتفظ بنسخة من ملفات النشر لكل إصدار
4. **الاختبار**: اختبر على جهاز نظيف قبل التوزيع

**آخر تحديث**: ديسمبر 2024
