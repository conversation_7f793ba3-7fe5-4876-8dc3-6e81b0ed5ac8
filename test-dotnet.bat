@echo off
echo Testing .NET installation...
echo.

echo Checking common .NET installation paths:
echo.

if exist "C:\Program Files\dotnet\dotnet.exe" (
    echo Found: C:\Program Files\dotnet\dotnet.exe
    "C:\Program Files\dotnet\dotnet.exe" --version
    echo.
    echo Setting up environment...
    set PATH=%PATH%;C:\Program Files\dotnet
    echo PATH updated
    echo.
    echo Testing dotnet command:
    dotnet --version
) else if exist "C:\Program Files (x86)\dotnet\dotnet.exe" (
    echo Found: C:\Program Files (x86)\dotnet\dotnet.exe
    "C:\Program Files (x86)\dotnet\dotnet.exe" --version
    echo.
    echo Setting up environment...
    set PATH=%PATH%;C:\Program Files (x86)\dotnet
    echo PATH updated
    echo.
    echo Testing dotnet command:
    dotnet --version
) else (
    echo .NET not found in common locations
    echo.
    echo Please install .NET 6.0 SDK from:
    echo https://dotnet.microsoft.com/download
    echo.
    echo Or check if it's installed in a different location
)

echo.
echo Current PATH:
echo %PATH%
echo.
pause
